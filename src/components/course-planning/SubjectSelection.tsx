'use client';

import React, { useState, useEffect } from 'react';
import {
	BookO<PERSON>,
	Users,
	Clock,
	CheckCircle2,
	Circle,
	Wand2,
	Sun,
	Sunset,
	Moon,
	RotateCcw,
	Calendar,
	AlertTriangle
} from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Alert, AlertDescription } from '@/components/ui/alert';
import {
	Select,
	SelectContent,
	SelectItem,
	SelectTrigger,
	SelectValue
} from '@/components/ui/select';
import { Badge } from '@/components/ui/badge';
import { useCoursePlanning } from '@/contexts/CoursePlanningContext';
import { Field, AutoMode } from '@/types/course-planning';

// AUTO_MODES configuration
const AUTO_MODES: { value: AutoMode; label: string; description: string; icon: React.ReactNode }[] =
	[
		{
			value: 'refer-non-overlap',
			label: '<PERSON><PERSON><PERSON> ưu tổng thể',
			description: '<PERSON><PERSON><PERSON> lịch học với ít xung đột nhất',
			icon: <Calendar className="h-4 w-4" />
		},
		{
			value: 'refer-non-overlap-morning',
			label: 'Ưu tiên buổi sáng',
			description: 'Tối ưu lịch học buổi sáng (tiết 1-6)',
			icon: <Sun className="h-4 w-4" />
		},
		{
			value: 'refer-non-overlap-afternoon',
			label: 'Ưu tiên buổi chiều',
			description: 'Tối ưu lịch học buổi chiều (tiết 7-12)',
			icon: <Sunset className="h-4 w-4" />
		},
		{
			value: 'refer-non-overlap-evening',
			label: 'Ưu tiên buổi tối',
			description: 'Tối ưu lịch học buổi tối (tiết 13-16)',
			icon: <Moon className="h-4 w-4" />
		}
	];

// SubjectCard component for individual subjects
interface SubjectCardProps {
	majorKey: string;
	subjectName: string;
	subjectData: any;
	isSelected: boolean;
	selectedClass: string | null;
	onToggleSubject: (checked: boolean) => void;
	onSelectClass: (classCode: string) => void;
	onCardClick: () => void;
	isActive: boolean;
}

function SubjectCard({
	majorKey: _majorKey,
	subjectName,
	subjectData,
	isSelected,
	selectedClass,
	onToggleSubject,
	onSelectClass: _onSelectClass,
	onCardClick,
	isActive
}: SubjectCardProps) {
	const totalClasses = Object.keys(subjectData).length;
	const firstClass = Object.values(subjectData)[0] as any;
	const teacher = firstClass?.[Field.Teacher] || 'N/A';

	const handleCardClick = () => {
		onToggleSubject(!isSelected);
		if (!isSelected) {
			onCardClick();
		}
	};

	return (
		<Card
			className={`cursor-pointer transition-all duration-200 hover:shadow-md ${
				isSelected ? 'ring-2 ring-primary bg-primary/5' : 'hover:bg-muted/30'
			} ${isActive ? 'ring-2 ring-blue-500' : ''}`}
			onClick={handleCardClick}
		>
			<CardContent className="p-4">
				<div className="flex items-start justify-between gap-3">
					<div className="flex items-start gap-3 flex-1">
						<div className="mt-1">
							{isSelected ? (
								<CheckCircle2 className="h-5 w-5 text-primary" />
							) : (
								<Circle className="h-5 w-5 text-muted-foreground" />
							)}
						</div>
						<div className="flex-1 space-y-2">
							<h4 className="font-medium text-sm leading-tight">{subjectName}</h4>
							<div className="flex items-center gap-3 text-xs text-muted-foreground">
								<span className="flex items-center gap-1">
									<Users className="h-3 w-3" />
									{totalClasses} lớp
								</span>
								<span className="flex items-center gap-1">
									<Clock className="h-3 w-3" />
									GV: {teacher}
								</span>
							</div>
							{isSelected && selectedClass && (
								<Badge variant="secondary" className="text-xs">
									Lớp: {selectedClass}
								</Badge>
							)}
						</div>
					</div>
				</div>
			</CardContent>
		</Card>
	);
}

// MajorSection component to group subjects by major
interface MajorSectionProps {
	majorKey: string;
	subjects: Array<{
		majorKey: string;
		subjectName: string;
		subjectData: any;
		isSelected: boolean;
		selectedClass: string | null;
		totalClasses: number;
	}>;
	onToggleSubject: (majorKey: string, subjectName: string, checked: boolean) => void;
	onSelectAllMajor: (majorKey: string) => void;
	onSubjectClick: () => void;
}

function MajorSection({
	majorKey,
	subjects,
	onToggleSubject,
	onSelectAllMajor,
	onSubjectClick
}: MajorSectionProps) {
	const selectedCount = subjects.filter((s) => s.isSelected).length;
	const totalCount = subjects.length;
	const allSelected = selectedCount === totalCount;

	return (
		<Card>
			<CardHeader>
				<div className="flex items-center justify-between">
					<div>
						<CardTitle className="text-lg">{majorKey}</CardTitle>
						<CardDescription>
							{selectedCount}/{totalCount} môn đã chọn
						</CardDescription>
					</div>
					<Button
						variant={allSelected ? 'default' : 'outline'}
						size="sm"
						onClick={() => onSelectAllMajor(majorKey)}
						className="flex items-center gap-2"
					>
						{allSelected ? (
							<>
								<CheckCircle2 className="h-4 w-4" />
								Bỏ chọn tất cả
							</>
						) : (
							<>
								<Circle className="h-4 w-4" />
								Chọn tất cả
							</>
						)}
					</Button>
				</div>
			</CardHeader>
			<CardContent>
				<div className="grid gap-3 sm:grid-cols-2">
					{subjects.map((subject) => (
						<SubjectCard
							key={`${subject.majorKey}-${subject.subjectName}`}
							majorKey={subject.majorKey}
							subjectName={subject.subjectName}
							subjectData={subject.subjectData}
							isSelected={subject.isSelected}
							selectedClass={subject.selectedClass}
							onToggleSubject={(checked) =>
								onToggleSubject(subject.majorKey, subject.subjectName, checked)
							}
							onSelectClass={() => {}}
							onCardClick={onSubjectClick}
							isActive={false}
						/>
					))}
				</div>
			</CardContent>
		</Card>
	);
}

// ScheduleWizard component for schedule generation
interface ScheduleWizardProps {
	selectedSubjectsCount: number;
	selectedClassesCount: number;
	onGenerateSchedule: (mode: AutoMode) => void;
	isLoading: boolean;
	autoTh: number;
	hasConflicts: boolean;
	conflictCount: number;
}

function ScheduleWizard({
	selectedSubjectsCount,
	selectedClassesCount,
	onGenerateSchedule,
	isLoading,
	autoTh,
	hasConflicts,
	conflictCount
}: ScheduleWizardProps) {
	const [selectedMode, setSelectedMode] = useState<AutoMode>('refer-non-overlap');
	const [currentStep, setCurrentStep] = useState(1);

	const canGenerate = selectedSubjectsCount > 0;
	const hasIncompleteSelection = selectedSubjectsCount > selectedClassesCount;

	// Auto-advance steps
	useEffect(() => {
		if (selectedSubjectsCount > 0 && currentStep === 1) {
			setCurrentStep(2);
		}
	}, [selectedSubjectsCount, currentStep]);

	const handleGenerateSchedule = () => {
		setCurrentStep(3);
		onGenerateSchedule(selectedMode);
	};

	if (selectedSubjectsCount === 0) {
		return (
			<Card>
				<CardHeader>
					<CardTitle className="flex items-center gap-2">
						<Wand2 className="h-5 w-5" />
						Tạo lịch học tự động
					</CardTitle>
					<CardDescription>Chọn ít nhất một môn học để bắt đầu tạo lịch</CardDescription>
				</CardHeader>
			</Card>
		);
	}

	return (
		<Card>
			<CardHeader>
				<CardTitle className="flex items-center gap-2">
					<Wand2 className="h-5 w-5" />
					Tạo lịch học tự động
				</CardTitle>
				<CardDescription>
					Tạo lịch học tối ưu từ {selectedSubjectsCount} môn đã chọn
				</CardDescription>
			</CardHeader>
			<CardContent className="space-y-6">
				{/* Summary */}
				<div className="flex items-center gap-4 p-4 bg-muted/50 rounded-lg">
					<div className="flex items-center gap-2">
						<Badge variant="outline">{selectedSubjectsCount} môn đã chọn</Badge>
						<Badge
							variant={selectedClassesCount === selectedSubjectsCount ? 'default' : 'secondary'}
						>
							{selectedClassesCount} lớp đã chọn
						</Badge>
						{autoTh >= 0 && (
							<Badge variant="secondary" className="flex items-center gap-1">
								<Calendar className="h-3 w-3" />
								Giải pháp #{autoTh + 1}
							</Badge>
						)}
					</div>
					{hasConflicts && (
						<Badge variant="destructive" className="flex items-center gap-1">
							<AlertTriangle className="h-3 w-3" />
							{conflictCount} tiết trùng
						</Badge>
					)}
				</div>

				{/* Warnings */}
				{hasIncompleteSelection && (
					<Alert>
						<AlertTriangle className="h-4 w-4" />
						<AlertDescription>
							Một số môn học chưa được chọn lớp. Hệ thống sẽ tự động chọn lớp tối ưu cho các môn
							này.
						</AlertDescription>
					</Alert>
				)}

				{/* Mode Selection */}
				<div className="space-y-3">
					<label className="text-sm font-medium">Chế độ tối ưu hóa:</label>
					<div className="grid gap-2">
						{AUTO_MODES.map((mode) => (
							<Card
								key={mode.value}
								className={`cursor-pointer transition-all ${
									selectedMode === mode.value
										? 'ring-2 ring-primary bg-primary/5'
										: 'hover:bg-muted/50'
								}`}
								onClick={() => setSelectedMode(mode.value)}
							>
								<CardContent className="p-3">
									<div className="flex items-center gap-3">
										<input
											type="radio"
											checked={selectedMode === mode.value}
											onChange={() => setSelectedMode(mode.value)}
											className="text-primary"
										/>
										{mode.icon}
										<div className="flex-1">
											<h4 className="font-medium text-sm">{mode.label}</h4>
											<p className="text-xs text-muted-foreground">{mode.description}</p>
										</div>
									</div>
								</CardContent>
							</Card>
						))}
					</div>
				</div>

				{/* Action Buttons */}
				<div className="flex gap-3">
					<Button
						onClick={handleGenerateSchedule}
						disabled={!canGenerate || isLoading}
						className="flex-1"
					>
						{isLoading ? (
							<>
								<div className="animate-spin rounded-full h-4 w-4 border-b-2 border-current mr-2"></div>
								Đang tạo lịch...
							</>
						) : autoTh >= 0 ? (
							<>
								<RotateCcw className="h-4 w-4 mr-2" />
								Tìm giải pháp khác
							</>
						) : (
							<>
								<Wand2 className="h-4 w-4 mr-2" />
								Tạo lịch tự động
							</>
						)}
					</Button>
				</div>

				{/* Results */}
				{autoTh >= 0 && !isLoading && (
					<div className="text-sm text-muted-foreground space-y-1">
						<p>Giải pháp thứ {autoTh + 1}</p>
						{hasConflicts ? (
							<p className="text-destructive">⚠️ {conflictCount} tiết học bị trùng</p>
						) : (
							<p className="text-green-600">✅ Không có xung đột thời gian</p>
						)}
					</div>
				)}
			</CardContent>
		</Card>
	);
}

// SubjectSummary component for the sidebar
interface SubjectSummaryProps {
	selectedSubjects: Array<{
		majorKey: string;
		subjectName: string;
		selectedClass: string | null;
		totalClasses: number;
		subjectData: any;
	}>;
	onClassSelect: (majorKey: string, subjectName: string, classCode: string) => void;
}

function SubjectSummary({ selectedSubjects, onClassSelect }: SubjectSummaryProps) {
	return (
		<Card>
			<CardHeader>
				<CardTitle className="text-lg">Môn đã chọn</CardTitle>
				<CardDescription>{selectedSubjects.length} môn học đã được chọn</CardDescription>
			</CardHeader>
			<CardContent className="space-y-4">
				{selectedSubjects.length === 0 ? (
					<p className="text-sm text-muted-foreground text-center py-4">
						Chưa có môn học nào được chọn
					</p>
				) : (
					selectedSubjects.map(
						({ majorKey, subjectName, selectedClass, totalClasses, subjectData }) => {
							const availableClasses = Object.keys(subjectData);

							return (
								<div key={`${majorKey}-${subjectName}`} className="p-3 rounded-lg border space-y-3">
									<div className="space-y-1">
										<h5 className="font-medium text-sm">{subjectName}</h5>
										<p className="text-xs text-muted-foreground">{majorKey}</p>
										<span className="text-xs text-muted-foreground">{totalClasses} lớp có sẵn</span>
									</div>

									{/* Class Selection */}
									<div className="space-y-2">
										<label className="text-xs font-medium text-muted-foreground">
											Chọn lớp học:
										</label>
										<Select
											value={selectedClass || ''}
											onValueChange={(value) => onClassSelect(majorKey, subjectName, value)}
										>
											<SelectTrigger className="h-8 text-xs">
												<SelectValue placeholder="Chọn lớp học" />
											</SelectTrigger>
											<SelectContent>
												{availableClasses.map((classCode) => {
													const classData = subjectData[classCode];
													const scheduleCount = classData.schedules.length;
													const teacher = classData[Field.Teacher] || 'N/A';

													return (
														<SelectItem key={classCode} value={classCode}>
															<div className="flex items-center justify-between w-full">
																<div>
																	<div className="font-medium text-xs">{classCode}</div>
																	<div className="text-xs text-muted-foreground">GV: {teacher}</div>
																</div>
																<div className="flex items-center gap-1 text-xs text-muted-foreground ml-2">
																	<Clock className="h-3 w-3" />
																	{scheduleCount} buổi
																</div>
															</div>
														</SelectItem>
													);
												})}
											</SelectContent>
										</Select>
									</div>
								</div>
							);
						}
					)
				)}
			</CardContent>
		</Card>
	);
}

export function SubjectSelection() {
	const {
		state,
		updateSelectedClass,
		updateShowSubject,
		selectMajor,
		generateSchedule,
		getCalendarTableData
	} = useCoursePlanning();
	const [searchTerm, setSearchTerm] = useState('');
	const [selectedMajor, setSelectedMajor] = useState<string>('all');

	if (!state.calendar) {
		return (
			<Card>
				<CardContent className="p-6">
					<div className="text-center text-muted-foreground">
						<BookOpen className="h-12 w-12 mx-auto mb-4 opacity-50" />
						<p>Vui lòng tải lên file Excel để bắt đầu chọn môn học</p>
					</div>
				</CardContent>
			</Card>
		);
	}

	// Get all subjects from all majors
	const allSubjects = Object.entries(state.calendar.majors).flatMap(([majorKey, majorData]) =>
		Object.entries(majorData).map(([subjectName, subjectData]) => ({
			majorKey,
			subjectName,
			subjectData,
			isSelected: state.selectedClasses[majorKey]?.[subjectName]?.show || false,
			selectedClass: state.selectedClasses[majorKey]?.[subjectName]?.class || null,
			totalClasses: Object.keys(subjectData).length
		}))
	);

	// Filter subjects based on search and major selection
	const filteredSubjects = allSubjects.filter((subject) => {
		const matchesSearch =
			subject.subjectName.toLowerCase().includes(searchTerm.toLowerCase()) ||
			subject.majorKey.toLowerCase().includes(searchTerm.toLowerCase());
		const matchesMajor = selectedMajor === 'all' || subject.majorKey === selectedMajor;
		return matchesSearch && matchesMajor;
	});

	// Get selected subjects for summary (with subjectData included)
	const selectedSubjects = allSubjects
		.filter((subject) => subject.isSelected)
		.map((subject) => ({
			majorKey: subject.majorKey,
			subjectName: subject.subjectName,
			selectedClass: subject.selectedClass,
			totalClasses: subject.totalClasses,
			subjectData: subject.subjectData
		}));

	// Get unique majors for filter
	const majors = Array.from(new Set(allSubjects.map((s) => s.majorKey)));

	const handleSubjectToggle = (majorKey: string, subjectName: string, checked: boolean) => {
		updateShowSubject(majorKey, subjectName, checked);
	};

	const handleClassSelect = (majorKey: string, subjectName: string, classCode: string) => {
		updateSelectedClass(majorKey, subjectName, classCode);
	};

	const handleSubjectClick = () => {
		// No longer needed since we removed activeSubject functionality
	};

	const handleSelectAllMajor = (majorKey: string) => {
		// Check if all subjects in this major are already selected
		const majorSubjects = allSubjects.filter(s => s.majorKey === majorKey);
		const selectedCount = majorSubjects.filter(s => s.isSelected).length;
		const allSelected = selectedCount === majorSubjects.length;

		// If all selected, deselect all; otherwise select all
		selectMajor(majorKey, !allSelected);
	};

	const handleGenerateSchedule = async (mode: AutoMode) => {
		await generateSchedule(mode);
	};

	// Get schedule data for wizard
	const calendarData = getCalendarTableData();
	const hasConflicts = calendarData && calendarData.totalConflictedSessions > 0;
	const conflictCount = calendarData?.totalConflictedSessions || 0;

	// Group subjects by major
	const subjectsByMajor = majors.reduce(
		(acc, majorKey) => {
			acc[majorKey] = allSubjects.filter((s) => s.majorKey === majorKey);
			return acc;
		},
		{} as Record<string, typeof allSubjects>
	);

	return (
		<div className="space-y-6">
			{/* Header and Search */}
			<Card>
				<CardHeader>
					<CardTitle className="flex items-center gap-2">
						<BookOpen className="h-5 w-5" />
						Chọn môn học và tạo lịch
					</CardTitle>
					<CardDescription>
						Chọn các môn học bạn muốn đăng ký, sau đó tạo lịch học tối ưu.
					</CardDescription>
				</CardHeader>
				<CardContent className="space-y-4">
					{/* Search and Filter */}
					<div className="flex gap-4">
						<div className="flex-1">
							<Input
								placeholder="Tìm kiếm môn học..."
								value={searchTerm}
								onChange={(e) => setSearchTerm(e.target.value)}
								className="w-full"
							/>
						</div>
						<Select value={selectedMajor} onValueChange={setSelectedMajor}>
							<SelectTrigger className="w-48">
								<SelectValue placeholder="Chọn chuyên ngành" />
							</SelectTrigger>
							<SelectContent>
								<SelectItem value="all">Tất cả chuyên ngành</SelectItem>
								{majors.map((major) => (
									<SelectItem key={major} value={major}>
										{major}
									</SelectItem>
								))}
							</SelectContent>
						</Select>
					</div>

					{/* Stats */}
					<div className="flex gap-4 text-sm text-muted-foreground">
						<span>Tổng: {filteredSubjects.length} môn</span>
						<span>Đã chọn: {selectedSubjects.length} môn</span>
					</div>
				</CardContent>
			</Card>

			<div className="grid gap-6 lg:grid-cols-3">
				{/* Main Content - Subject Selection */}
				<div className="lg:col-span-2 space-y-6">
					{/* Subjects by Major */}
					{selectedMajor === 'all' ? (
						// Show all majors with their subjects
						majors.map((majorKey) => {
							const majorSubjects = (subjectsByMajor[majorKey] || []).filter((subject) => {
								const matchesSearch = subject.subjectName
									.toLowerCase()
									.includes(searchTerm.toLowerCase());
								return matchesSearch;
							});

							if (majorSubjects.length === 0) return null;

							return (
								<MajorSection
									key={majorKey}
									majorKey={majorKey}
									subjects={majorSubjects}
									onToggleSubject={handleSubjectToggle}
									onSelectAllMajor={handleSelectAllMajor}
									onSubjectClick={handleSubjectClick}
								/>
							);
						})
					) : (
						// Show only selected major
						<MajorSection
							majorKey={selectedMajor}
							subjects={filteredSubjects}
							onToggleSubject={handleSubjectToggle}
							onSelectAllMajor={handleSelectAllMajor}
							onSubjectClick={handleSubjectClick}
						/>
					)}

					{filteredSubjects.length === 0 && (
						<Card>
							<CardContent className="p-8">
								<div className="text-center text-muted-foreground">
									<BookOpen className="h-12 w-12 mx-auto mb-4 opacity-50" />
									<p>Không tìm thấy môn học nào phù hợp</p>
								</div>
							</CardContent>
						</Card>
					)}
				</div>

				{/* Sidebar */}
				<div className="space-y-6">
					{/* Selected Subjects Summary */}
					<SubjectSummary selectedSubjects={selectedSubjects} onClassSelect={handleClassSelect} />

					{/* Schedule Generation Wizard */}
					<ScheduleWizard
						selectedSubjectsCount={selectedSubjects.length}
						selectedClassesCount={selectedSubjects.filter((s) => s.selectedClass).length}
						onGenerateSchedule={handleGenerateSchedule}
						isLoading={state.loading || false}
						autoTh={state.autoTh}
						hasConflicts={hasConflicts || false}
						conflictCount={conflictCount}
					/>
				</div>
			</div>
		</div>
	);
}
