'use client';

import React, { useState } from 'react';
import { <PERSON><PERSON><PERSON>, <PERSON>, Clock, CheckCircle2, Circle } from 'lucide-react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import {
	Select,
	SelectContent,
	SelectItem,
	SelectTrigger,
	SelectValue
} from '@/components/ui/select';
import { Badge } from '@/components/ui/badge';
import { useCoursePlanning } from '@/contexts/CoursePlanningContext';
import { Field } from '@/types/course-planning';
import { ClassSelector } from './ClassSelector';

// SubjectCard component for individual subjects
interface SubjectCardProps {
	majorKey: string;
	subjectName: string;
	subjectData: any;
	isSelected: boolean;
	selectedClass: string | null;
	onToggleSubject: (checked: boolean) => void;
	onSelectClass: (classCode: string) => void;
	onCardClick: () => void;
	isActive: boolean;
}

function SubjectCard({
	_majorKey,
	subjectName,
	subjectData,
	isSelected,
	selectedClass,
	onToggleSubject,
	_onSelectClass,
	onCardClick,
	isActive
}: SubjectCardProps) {
	const totalClasses = Object.keys(subjectData).length;
	const firstClass = Object.values(subjectData)[0] as any;
	const teacher = firstClass?.[Field.Teacher] || 'N/A';

	return (
		<Card
			className={`cursor-pointer transition-all duration-200 hover:shadow-md ${
				isSelected ? 'ring-2 ring-primary bg-primary/5' : ''
			} ${isActive ? 'ring-2 ring-blue-500' : ''}`}
			onClick={onCardClick}
		>
			<CardContent className="p-4">
				<div className="flex items-start justify-between gap-3">
					<div className="flex items-start gap-3 flex-1">
						<div
							className="mt-1"
							onClick={(e) => {
								e.stopPropagation();
								onToggleSubject(!isSelected);
							}}
						>
							{isSelected ? (
								<CheckCircle2 className="h-5 w-5 text-primary" />
							) : (
								<Circle className="h-5 w-5 text-muted-foreground" />
							)}
						</div>
						<div className="flex-1 space-y-2">
							<h4 className="font-medium text-sm leading-tight">{subjectName}</h4>
							<div className="flex items-center gap-3 text-xs text-muted-foreground">
								<span className="flex items-center gap-1">
									<Users className="h-3 w-3" />
									{totalClasses} lớp
								</span>
								<span className="flex items-center gap-1">
									<Clock className="h-3 w-3" />
									GV: {teacher}
								</span>
							</div>
							{isSelected && selectedClass && (
								<Badge variant="secondary" className="text-xs">
									Lớp: {selectedClass}
								</Badge>
							)}
						</div>
					</div>
				</div>
			</CardContent>
		</Card>
	);
}

// SubjectSummary component for the sidebar
interface SubjectSummaryProps {
	selectedSubjects: Array<{
		majorKey: string;
		subjectName: string;
		selectedClass: string | null;
		totalClasses: number;
	}>;
	activeSubject: { majorKey: string; subjectName: string } | null;
	onSubjectClick: (majorKey: string, subjectName: string) => void;
}

function SubjectSummary({ selectedSubjects, activeSubject, onSubjectClick }: SubjectSummaryProps) {
	return (
		<Card>
			<CardHeader>
				<CardTitle className="text-lg">Môn đã chọn</CardTitle>
				<CardDescription>{selectedSubjects.length} môn học đã được chọn</CardDescription>
			</CardHeader>
			<CardContent className="space-y-3">
				{selectedSubjects.length === 0 ? (
					<p className="text-sm text-muted-foreground text-center py-4">
						Chưa có môn học nào được chọn
					</p>
				) : (
					selectedSubjects.map(({ majorKey, subjectName, selectedClass, totalClasses }) => {
						const isActive =
							activeSubject?.majorKey === majorKey && activeSubject?.subjectName === subjectName;

						return (
							<div
								key={`${majorKey}-${subjectName}`}
								className={`p-3 rounded-lg border cursor-pointer transition-colors ${
									isActive ? 'bg-blue-50 border-blue-200' : 'hover:bg-muted/50'
								}`}
								onClick={() => onSubjectClick(majorKey, subjectName)}
							>
								<div className="space-y-1">
									<h5 className="font-medium text-sm">{subjectName}</h5>
									<p className="text-xs text-muted-foreground">{majorKey}</p>
									<div className="flex items-center justify-between">
										<span className="text-xs text-muted-foreground">{totalClasses} lớp có sẵn</span>
										{selectedClass ? (
											<Badge variant="default" className="text-xs">
												{selectedClass}
											</Badge>
										) : (
											<Badge variant="outline" className="text-xs">
												Chưa chọn lớp
											</Badge>
										)}
									</div>
								</div>
							</div>
						);
					})
				)}
			</CardContent>
		</Card>
	);
}

export function SubjectSelection() {
	const { state, updateSelectedClass, updateShowSubject } = useCoursePlanning();
	const [searchTerm, setSearchTerm] = useState('');
	const [selectedMajor, setSelectedMajor] = useState<string>('all');
	const [activeSubject, setActiveSubject] = useState<{
		majorKey: string;
		subjectName: string;
	} | null>(null);

	if (!state.calendar) {
		return (
			<Card>
				<CardContent className="p-6">
					<div className="text-center text-muted-foreground">
						<BookOpen className="h-12 w-12 mx-auto mb-4 opacity-50" />
						<p>Vui lòng tải lên file Excel để bắt đầu chọn môn học</p>
					</div>
				</CardContent>
			</Card>
		);
	}

	// Get all subjects from all majors
	const allSubjects = Object.entries(state.calendar.majors).flatMap(([majorKey, majorData]) =>
		Object.entries(majorData).map(([subjectName, subjectData]) => ({
			majorKey,
			subjectName,
			subjectData,
			isSelected: state.selectedClasses[majorKey]?.[subjectName]?.show || false,
			selectedClass: state.selectedClasses[majorKey]?.[subjectName]?.class || null,
			totalClasses: Object.keys(subjectData).length
		}))
	);

	// Filter subjects based on search and major selection
	const filteredSubjects = allSubjects.filter((subject) => {
		const matchesSearch =
			subject.subjectName.toLowerCase().includes(searchTerm.toLowerCase()) ||
			subject.majorKey.toLowerCase().includes(searchTerm.toLowerCase());
		const matchesMajor = selectedMajor === 'all' || subject.majorKey === selectedMajor;
		return matchesSearch && matchesMajor;
	});

	// Get selected subjects for summary
	const selectedSubjects = allSubjects.filter((subject) => subject.isSelected);

	// Get unique majors for filter
	const majors = Array.from(new Set(allSubjects.map((s) => s.majorKey)));

	const handleSubjectToggle = (majorKey: string, subjectName: string, checked: boolean) => {
		updateShowSubject(majorKey, subjectName, checked);
	};

	const handleClassSelect = (majorKey: string, subjectName: string, classCode: string) => {
		updateSelectedClass(majorKey, subjectName, classCode);
	};

	const handleSubjectClick = (majorKey: string, subjectName: string) => {
		setActiveSubject({ majorKey, subjectName });
	};

	return (
		<div className="grid gap-6 lg:grid-cols-3">
			{/* Main Content */}
			<div className="lg:col-span-2 space-y-6">
				{/* Header and Filters */}
				<Card>
					<CardHeader>
						<CardTitle className="flex items-center gap-2">
							<BookOpen className="h-5 w-5" />
							Chọn môn học
						</CardTitle>
						<CardDescription>
							Chọn các môn học bạn muốn đăng ký. Click vào môn học để xem chi tiết và chọn lớp.
						</CardDescription>
					</CardHeader>
					<CardContent className="space-y-4">
						{/* Search and Filter */}
						<div className="flex gap-4">
							<div className="flex-1">
								<Input
									placeholder="Tìm kiếm môn học..."
									value={searchTerm}
									onChange={(e) => setSearchTerm(e.target.value)}
									className="w-full"
								/>
							</div>
							<Select value={selectedMajor} onValueChange={setSelectedMajor}>
								<SelectTrigger className="w-48">
									<SelectValue placeholder="Chọn chuyên ngành" />
								</SelectTrigger>
								<SelectContent>
									<SelectItem value="all">Tất cả chuyên ngành</SelectItem>
									{majors.map((major) => (
										<SelectItem key={major} value={major}>
											{major}
										</SelectItem>
									))}
								</SelectContent>
							</Select>
						</div>

						{/* Stats */}
						<div className="flex gap-4 text-sm text-muted-foreground">
							<span>Tổng: {filteredSubjects.length} môn</span>
							<span>Đã chọn: {selectedSubjects.length} môn</span>
						</div>
					</CardContent>
				</Card>

				{/* Subjects Grid */}
				<div className="grid gap-4 sm:grid-cols-2">
					{filteredSubjects.map((subject) => (
						<SubjectCard
							key={`${subject.majorKey}-${subject.subjectName}`}
							majorKey={subject.majorKey}
							subjectName={subject.subjectName}
							subjectData={subject.subjectData}
							isSelected={subject.isSelected}
							selectedClass={subject.selectedClass}
							onToggleSubject={(checked) =>
								handleSubjectToggle(subject.majorKey, subject.subjectName, checked)
							}
							onSelectClass={(classCode) =>
								handleClassSelect(subject.majorKey, subject.subjectName, classCode)
							}
							onCardClick={() => handleSubjectClick(subject.majorKey, subject.subjectName)}
							isActive={
								activeSubject?.majorKey === subject.majorKey &&
								activeSubject?.subjectName === subject.subjectName
							}
						/>
					))}
				</div>

				{filteredSubjects.length === 0 && (
					<Card>
						<CardContent className="p-8">
							<div className="text-center text-muted-foreground">
								<BookOpen className="h-12 w-12 mx-auto mb-4 opacity-50" />
								<p>Không tìm thấy môn học nào phù hợp</p>
							</div>
						</CardContent>
					</Card>
				)}
			</div>

			{/* Sidebar */}
			<div className="space-y-6">
				<SubjectSummary
					selectedSubjects={selectedSubjects}
					activeSubject={activeSubject}
					onSubjectClick={handleSubjectClick}
				/>

				{/* Class Selection for Active Subject */}
				{activeSubject && (
					<ClassSelector
						majorKey={activeSubject.majorKey}
						subjectName={activeSubject.subjectName}
						onClassSelect={handleClassSelect}
					/>
				)}
			</div>
		</div>
	);
}
